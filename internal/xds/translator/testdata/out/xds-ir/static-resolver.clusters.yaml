- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig:
    localityWeightedLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: first-route-dest
  ignoreHealthOnHostRemoval: true
  metadata:
    filterMetadata:
      envoy.gateway.static_resolver:
        override_host_sources:
        - header: target-pod
          type: header
  name: first-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig:
    localityWeightedLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: second-route-dest
  ignoreHealthOnHostRemoval: true
  metadata:
    filterMetadata:
      envoy.gateway.static_resolver:
        override_host_sources:
        - key: envoy.lb
          path:
          - target-pod
          type: metadata
  name: second-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig:
    localityWeightedLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: third-route-dest
  ignoreHealthOnHostRemoval: true
  metadata:
    filterMetadata:
      envoy.gateway.static_resolver:
        override_host_sources:
        - header: target-pod
          type: header
        - header: route-strategy
          type: header
        - key: envoy.lb
          path:
          - override-host
          type: metadata
  name: third-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
