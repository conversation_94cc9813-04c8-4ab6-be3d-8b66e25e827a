apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-static-resolver-header
  namespace: envoy-gateway
spec:
  type: StaticResolver
  endpoints:
  - ip:
      address: "********"
      port: 8080
  - ip:
      address: "********"
      port: 8080
  staticResolver:
    overrideHostSources:
    - header: "target-pod"
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-static-resolver-metadata
  namespace: envoy-gateway
spec:
  type: StaticResolver
  endpoints:
  - ip:
      address: "********"
      port: 8080
  - ip:
      address: "********"
      port: 8080
  staticResolver:
    overrideHostSources:
    - metadata:
        key: "envoy.lb"
        path:
        - key: "target-pod"
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-static-resolver-multiple-sources
  namespace: envoy-gateway
spec:
  type: StaticResolver
  endpoints:
  - ip:
      address: "********"
      port: 8080
  - ip:
      address: "********"
      port: 8080
  - ip:
      address: "********"
      port: 8080
  staticResolver:
    overrideHostSources:
    - header: "target-pod"
    - header: "route-strategy"
    - metadata:
        key: "envoy.lb"
        path:
        - key: "override-host"
